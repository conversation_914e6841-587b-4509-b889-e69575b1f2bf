'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface TypingUser {
  user_id: string;
  user_name: string;
  is_support: boolean;
  timestamp: string;
}

interface TypingIndicatorProps {
  typingUsers: TypingUser[];
  className?: string;
  showUserNames?: boolean;
  maxDisplayUsers?: number;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  typingUsers,
  className = '',
  showUserNames = true,
  maxDisplayUsers = 3
}) => {
  if (typingUsers.length === 0) {
    return null;
  }

  // Generate avatar color based on user name hash
  const generateAvatarColor = (name: string): string => {
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }

    const colors = [
      '#133157', // Primary blue
      '#1e40af', // Blue-700
      '#7c3aed', // Violet-600
      '#dc2626', // Red-600
      '#ea580c', // Orange-600
      '#16a34a', // Green-600
      '#0891b2', // Cyan-600
      '#be185d', // Pink-600
    ];

    return colors[Math.abs(hash) % colors.length];
  };

  const displayUsers = typingUsers.slice(0, maxDisplayUsers);
  const remainingCount = Math.max(0, typingUsers.length - maxDisplayUsers);

  const getTypingText = () => {
    if (!showUserNames) {
      return typingUsers.length === 1 ? 'Qualcuno sta scrivendo...' : `${typingUsers.length} persone stanno scrivendo...`;
    }

    if (displayUsers.length === 1) {
      return `${displayUsers[0].user_name} sta scrivendo...`;
    } else if (displayUsers.length === 2) {
      return `${displayUsers[0].user_name} e ${displayUsers[1].user_name} stanno scrivendo...`;
    } else if (displayUsers.length === 3 && remainingCount === 0) {
      return `${displayUsers[0].user_name}, ${displayUsers[1].user_name} e ${displayUsers[2].user_name} stanno scrivendo...`;
    } else {
      const names = displayUsers.map(u => u.user_name).join(', ');
      return `${names}${remainingCount > 0 ? ` e altri ${remainingCount}` : ''} stanno scrivendo...`;
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 10, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -10, scale: 0.95 }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 25,
          duration: 0.3
        }}
        className={`typing-indicator-container flex items-center gap-3 p-3 mx-4 mb-4 rounded-2xl bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200 shadow-sm relative ${className}`}
      >
        {/* User Avatars */}
        <div className="flex -space-x-2">
          {displayUsers.map((user, index) => (
            <motion.div
              key={user.user_id}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: index * 0.1 }}
              className="relative"
            >
              <Avatar
                className="w-6 h-6 border-2 border-white shadow-sm"
                style={{ backgroundColor: generateAvatarColor(user.user_name) }}
              >
                <AvatarFallback className="text-white font-semibold bg-transparent text-xs">
                  {user.user_name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              {user.is_support && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-[#febd49] rounded-full border border-white flex items-center justify-center">
                  <div className="w-1.5 h-1.5 bg-[#133157] rounded-full"></div>
                </div>
              )}
            </motion.div>
          ))}
          {remainingCount > 0 && (
            <motion.div
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="w-6 h-6 bg-gray-400 rounded-full border-2 border-white shadow-sm flex items-center justify-center"
            >
              <span className="text-white text-xs font-semibold">
                +{remainingCount}
              </span>
            </motion.div>
          )}
        </div>

        {/* Typing Text and Animation */}
        <div className="flex items-center gap-2 flex-1">
          <motion.span
            className="text-sm text-gray-600 font-medium"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            {getTypingText()}
          </motion.span>

          {/* Animated Typing Dots */}
          <div className="flex items-center gap-1">
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                className="w-1.5 h-1.5 bg-[#133157] rounded-full"
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.4, 1, 0.4],
                }}
                transition={{
                  duration: 1.4,
                  repeat: Infinity,
                  delay: index * 0.2,
                  ease: "easeInOut",
                }}
              />
            ))}
          </div>
        </div>

        {/* Pulse Animation Background */}
        <motion.div
          className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#133157]/5 to-[#febd49]/5 -z-10"
          animate={{
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </motion.div>
    </AnimatePresence>
  );
};
