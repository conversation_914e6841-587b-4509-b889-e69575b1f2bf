'use client';

import { useContext, useEffect } from 'react';
import { AuthContext } from '@/components/context/AuthContext';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageSquare, Clock, Inbox, CheckCircle, 
  AlertTriangle, Archive, BarChart, Users, 
  Settings, LogOut, HeadphonesIcon 
} from 'lucide-react';

const Sidebar = () => {
  const { isSidepanelOpen, setIsSidepanelOpen, logout } = useContext(AuthContext);
  const pathname = usePathname();

  // Prevent body scrolling when sidebar is open on mobile
  useEffect(() => {
    if (isSidepanelOpen) {
      const originalStyle = window.getComputedStyle(document.body).overflow;
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = originalStyle;
      };
    }
  }, [isSidepanelOpen]);
  
  const menuItems = [
    { 
      label: '<PERSON><PERSON> le Chat', 
      icon: <MessageSquare size={20} />, 
      href: '/dashboard', 
      active: pathname === '/dashboard'
    },
    { 
      label: 'In Attesa',
      icon: <Clock size={20} />, 
      href: '/dashboard?status=pending',
      active: pathname.includes('pending')
    },
    { 
      label: 'In Corso', 
      icon: <Inbox size={20} />, 
      href: '/dashboard?status=in_progress',
      active: pathname.includes('in_progress')
    },
    { 
      label: 'Risolte', 
      icon: <CheckCircle size={20} />, 
      href: '/dashboard?status=resolved',
      active: pathname.includes('resolved')
    },
    { 
      label: 'Alta Priorità', 
      icon: <AlertTriangle size={20} />, 
      href: '/dashboard?priority=high',
      active: pathname.includes('priority=high')
    },
    { 
      label: 'Archivio', 
      icon: <Archive size={20} />, 
      href: '/dashboard/archive',
      active: pathname.includes('/archive')
    },
    { 
      label: 'Rapporti', 
      icon: <BarChart size={20} />, 
      href: '/dashboard/reports',
      active: pathname.includes('/reports')
    },
    { 
      label: 'Team', 
      icon: <Users size={20} />, 
      href: '/dashboard/team',
      active: pathname.includes('/team')
    },
    { 
      label: 'Impostazioni', 
      icon: <Settings size={20} />, 
      href: '/dashboard/settings',
      active: pathname.includes('/settings')
    }
  ];
  
  const sidebarVariants = {
    hidden: { x: -300, opacity: 0 },
    visible: { 
      x: 0, 
      opacity: 1,
      transition: { 
        type: "spring", 
        stiffness: 300, 
        damping: 30,
        duration: 0.3
      }
    },
    exit: { 
      x: -300, 
      opacity: 0,
      transition: { 
        ease: "easeInOut",
        duration: 0.2
      }
    }
  };

  const menuItemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: (i: number) => ({
      opacity: 1,
      x: 0,
      transition: {
        delay: i * 0.05,
        duration: 0.2
      }
    }),
    hover: { 
      scale: 1.03,
      transition: { duration: 0.2 }
    }
  };
  
  return (
    <AnimatePresence>
      {isSidepanelOpen && (
        <>
          {/* Mobile overlay */}
          <motion.div 
            className="md:hidden fixed inset-0 bg-black/50 z-10"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsSidepanelOpen(false)}
          />
          {/* Sidebar */}
          <motion.div 
            className="bg-[#f9f9f9] text-black h-screen w-72 flex flex-col overflow-hidden border-r border-[#febd49]/20 shadow-lg z-20 fixed left-0 top-0"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={sidebarVariants}
          >
            {/* Header section that matches the main header */}
            <div className="h-16 border-b border-[#febd49]/20 flex items-center px-4 bg-[#f9f9f9] shadow-sm">
              <h2 className="text-xl font-bold flex items-center gap-2 text-[#113158]">
                <HeadphonesIcon className="text-[#febd49] h-5 w-5" />
                Support Portal
              </h2>
            </div>
            
            <motion.div 
              className="hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 0 }}
            >
              <h2 className="text-xl font-bold flex items-center gap-2 text-[#113158]">
                <HeadphonesIcon className="text-[#febd49] h-5 w-5" />
                Support Portal
              </h2>
            </motion.div>
            
            <nav className="flex-grow p-5 overflow-y-auto">
              <div className="mb-6">
                <motion.h3 
                  className="font-medium text-xs uppercase tracking-wider text-[#113158] mb-3 px-3"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  Navigazione
                </motion.h3>
                <ul className="space-y-2.5">
                  {menuItems.slice(0, 6).map((item, i) => (
                    <motion.li 
                      key={item.label}
                      custom={i}
                      initial="hidden"
                      animate="visible"
                      variants={menuItemVariants}
                      whileHover="hover"
                    >
                      <Link 
                        href={item.href}
                        className={`flex items-center gap-3 px-3 py-2.5 rounded-md transition-all ${
                          item.active 
                            ? 'bg-[#113158] text-white shadow-sm' 
                            : 'hover:bg-[#febd49]/10 text-black hover:text-[#113158]'
                        }`}
                      >
                        <span className={item.active ? 'text-white' : 'text-[#113158]'}>
                          {item.icon}
                        </span>
                        <span className={`font-medium ${item.active ? 'text-white' : ''}`}>
                          {item.label}
                        </span>
                      </Link>
                    </motion.li>
                  ))}
                </ul>
              </div>
              
              <div className="mb-4">
                <motion.h3 
                  className="font-medium text-xs uppercase tracking-wider text-[#113158] mb-3 px-3"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  Gestione
                </motion.h3>
                <ul className="space-y-2.5">
                  {menuItems.slice(6).map((item, i) => (
                    <motion.li 
                      key={item.label}
                      custom={i + 6}
                      initial="hidden"
                      animate="visible"
                      variants={menuItemVariants}
                      whileHover="hover"
                    >
                      <Link 
                        href={item.href}
                        className={`flex items-center gap-3 px-3 py-2.5 rounded-md transition-all ${
                          item.active 
                            ? 'bg-[#113158] text-white shadow-sm' 
                            : 'hover:bg-[#febd49]/10 text-black hover:text-[#113158]'
                        }`}
                      >
                        <span className={item.active ? 'text-white' : 'text-[#113158]'}>
                          {item.icon}
                        </span>
                        <span className={`font-medium ${item.active ? 'text-white' : ''}`}>
                          {item.label}
                        </span>
                      </Link>
                    </motion.li>
                  ))}
                </ul>
              </div>
            </nav>
              <motion.div 
              className="p-5 mt-auto border-t border-[#febd49]/20 bg-[#f9f9f9]"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <motion.button 
                className="w-full flex items-center justify-center gap-2 rounded-md bg-[#113158] hover:opacity-90 text-white py-2 px-4 font-medium transition-colors shadow-sm"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={async () => {
                  try {
                    // Use the proper logout function from AuthContext
                    await logout();
                  } catch (error) {
                    console.error("Error during logout:", error);
                  }
                }}
              >
                <LogOut className="h-4 w-4" />
                Esci
              </motion.button>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default Sidebar;
