/**
 * WebSocket utility functions for admin support chat
 * Implements file upload integration and validation as per backend documentation
 */

import { WebSocketMessage } from './websocketService';

// Supported file types as per backend documentation
export const SUPPORTED_FILE_TYPES = {
  'application/pdf': '.pdf',
  'image/jpeg': '.jpg',
  'image/jpg': '.jpg', 
  'image/png': '.png',
  'application/msword': '.doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
  'text/plain': '.txt'
} as const;

export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB as per backend constraints

// File validation function
export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  fileType?: string;
}

export const validateFile = (file: File): FileValidationResult => {
  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File size exceeds 5MB limit. Current size: ${(file.size / 1024 / 1024).toFixed(2)}MB`
    };
  }

  // Check file type
  const supportedTypes = Object.keys(SUPPORTED_FILE_TYPES);
  if (!supportedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `Unsupported file type: ${file.type}. Supported types: PDF, Images (JPG, PNG), Documents (DOC, DOCX), Text files`
    };
  }

  return {
    isValid: true,
    fileType: file.type
  };
};

// File upload progress tracking
export interface UploadProgressCallback {
  (progress: number, messageId: string): void;
}

// WebSocket file upload notification as per backend documentation
export const createFileUploadNotification = (messageId: string): WebSocketMessage => {
  return {
    type: 'file.upload.notify',
    message_id: messageId
  };
};

// Create typing start message
export const createTypingStartMessage = (): WebSocketMessage => {
  return {
    type: 'typing.start'
  };
};

// Create typing stop message
export const createTypingStopMessage = (): WebSocketMessage => {
  return {
    type: 'typing.stop'
  };
};

// Create mark single message as read
export const createMarkMessageReadMessage = (messageId: string): WebSocketMessage => {
  return {
    type: 'message.mark_read',
    message_id: messageId
  };
};

// Create mark multiple messages as read (max 50 per backend docs)
export const createMarkMessagesReadMessage = (messageIds: string[]): WebSocketMessage => {
  if (messageIds.length > 50) {
    throw new Error('Cannot mark more than 50 messages as read at once');
  }
  return {
    type: 'messages.mark_read',
    message_ids: messageIds
  };
};

// Create chat message
export const createChatMessage = (message: string): WebSocketMessage => {
  return {
    type: 'chat.message',
    message
  };
};

// Create ping message
export const createPingMessage = (): WebSocketMessage => {
  return {
    type: 'ping'
  };
};

// Connection health check utilities
export interface ConnectionHealthStatus {
  isHealthy: boolean;
  latency?: number;
  lastPing?: Date;
  consecutiveFailures: number;
}

export class ConnectionHealthMonitor {
  private pingStartTime: number = 0;
  private consecutiveFailures: number = 0;
  private lastPing: Date | null = null;

  public startPing(): void {
    this.pingStartTime = Date.now();
  }

  public recordPong(): number {
    const latency = Date.now() - this.pingStartTime;
    this.consecutiveFailures = 0;
    this.lastPing = new Date();
    return latency;
  }

  public recordFailure(): void {
    this.consecutiveFailures++;
  }

  public getHealthStatus(): ConnectionHealthStatus {
    const isHealthy = this.consecutiveFailures < 3; // Allow up to 2 consecutive failures
    
    return {
      isHealthy,
      latency: this.lastPing ? Date.now() - this.pingStartTime : undefined,
      lastPing: this.lastPing || undefined,
      consecutiveFailures: this.consecutiveFailures
    };
  }

  public reset(): void {
    this.consecutiveFailures = 0;
    this.lastPing = null;
    this.pingStartTime = 0;
  }
}

// Message deduplication utility
export class MessageDeduplicator {
  private seenMessages: Set<string> = new Set();
  private maxSize: number = 1000; // Keep track of last 1000 message IDs

  public isDuplicate(messageId: string): boolean {
    if (this.seenMessages.has(messageId)) {
      return true;
    }

    // Add to seen messages
    this.seenMessages.add(messageId);

    // Clean up if we exceed max size
    if (this.seenMessages.size > this.maxSize) {
      const firstItem = this.seenMessages.values().next().value;
      if (firstItem !== undefined) {
        this.seenMessages.delete(firstItem);
      }
    }

    return false;
  }

  public clear(): void {
    this.seenMessages.clear();
  }
}

// Rate limiting for typing indicators
export class TypingRateLimiter {
  private lastTypingSent: Map<string, number> = new Map();
  private readonly TYPING_RATE_LIMIT = 3000; // 3 seconds as per implementation

  public canSendTyping(chatId: string): boolean {
    const lastSent = this.lastTypingSent.get(chatId);
    const now = Date.now();
    
    if (!lastSent || now - lastSent > this.TYPING_RATE_LIMIT) {
      this.lastTypingSent.set(chatId, now);
      return true;
    }
    
    return false;
  }

  public clear(chatId?: string): void {
    if (chatId) {
      this.lastTypingSent.delete(chatId);
    } else {
      this.lastTypingSent.clear();
    }
  }
}

// WebSocket URL validation
export const validateWebSocketUrl = (url: string): boolean => {
  try {
    const wsUrl = new URL(url);
    return wsUrl.protocol === 'ws:' || wsUrl.protocol === 'wss:';
  } catch {
    return false;
  }
};

// Error code mapping as per documentation
export const getWebSocketErrorDescription = (code: number): string => {
  switch (code) {
    case 4001:
      return 'No authentication token provided';
    case 4002:
      return 'Invalid authentication token';
    case 4003:
      return 'Token authentication error';
    case 4004:
      return 'No chat ID provided in URL';
    case 4005:
      return 'Chat not found';
    case 4006:
      return 'Permission denied (admin access required)';
    case 4007:
      return 'Error getting chat details';
    default:
      return `Unknown WebSocket error (code: ${code})`;
  }
};

// Connection priority utilities
export type ChatPriority = 'low' | 'medium' | 'high' | 'urgent';

export const getPriorityScore = (priority: ChatPriority): number => {
  const scores = { low: 1, medium: 2, high: 3, urgent: 4 };
  return scores[priority];
};

export const shouldAutoConnect = (priority: ChatPriority): boolean => {
  return priority === 'urgent' || priority === 'high';
};

// Message size validation - updated to 5000 characters as per backend docs
export const MAX_MESSAGE_SIZE = 5000; // 5000 characters limit for messages

export const validateMessageSize = (message: string): boolean => {
  return message.length <= MAX_MESSAGE_SIZE;
};

// Rate limiting for messages - 30 messages per minute as per backend docs
export class MessageRateLimiter {
  private messageTimes: Map<string, number[]> = new Map();
  private readonly MESSAGE_RATE_LIMIT = 30; // 30 messages per minute
  private readonly RATE_WINDOW = 60000; // 1 minute in milliseconds

  public canSendMessage(chatId: string): boolean {
    const now = Date.now();
    const times = this.messageTimes.get(chatId) || [];

    // Remove times older than 1 minute
    const recentTimes = times.filter(time => now - time < this.RATE_WINDOW);

    if (recentTimes.length >= this.MESSAGE_RATE_LIMIT) {
      return false;
    }

    // Add current time and update
    recentTimes.push(now);
    this.messageTimes.set(chatId, recentTimes);
    return true;
  }

  public getTimeUntilNextMessage(chatId: string): number {
    const now = Date.now();
    const times = this.messageTimes.get(chatId) || [];

    if (times.length < this.MESSAGE_RATE_LIMIT) {
      return 0;
    }

    const oldestTime = Math.min(...times);
    return Math.max(0, this.RATE_WINDOW - (now - oldestTime));
  }

  public clear(chatId?: string): void {
    if (chatId) {
      this.messageTimes.delete(chatId);
    } else {
      this.messageTimes.clear();
    }
  }
}

// Exponential backoff calculator
export const calculateBackoffDelay = (attempt: number, baseDelay: number = 1000, maxDelay: number = 30000): number => {
  return Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
};
